#!/usr/bin/env python3
"""
测试修改后的DBLP爬虫
"""

import os
import sys
import json
import logging

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import get_dblp_papers

def test_crawler():
    """测试修改后的爬虫功能"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 测试一个小的会议
    venue = 'vldb'
    year = 2023
    
    print(f"🚀 开始测试获取 {venue} {year} 的论文...")
    
    try:
        filepath = get_dblp_papers(venue, year)
        
        if filepath:
            print(f"✅ 成功保存到: {filepath}")
            
            # 读取并检查数据结构
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("\n📊 数据结构检查:")
            print(f"- 顶级键: {list(data.keys())}")
            
            if 'metadata' in data:
                print(f"- metadata 键: {list(data['metadata'].keys())}")
                print(f"- 过滤后论文总数: {data['metadata']['total_papers']}")
                if 'type_distribution' in data['metadata']:
                    print(f"- 过滤后类型分布: {data['metadata']['type_distribution']}")
            
            if 'metadata_before_filtered' in data:
                print(f"- metadata_before_filtered 键: {list(data['metadata_before_filtered'].keys())}")
                print(f"- 过滤前论文总数: {data['metadata_before_filtered']['total_papers']}")
                if 'type_distribution' in data['metadata_before_filtered']:
                    print(f"- 过滤前类型分布: {data['metadata_before_filtered']['type_distribution']}")
            
            print(f"- 论文数据条数: {len(data.get('papers', []))}")
            
        else:
            print("❌ 没有获取到数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_crawler()
